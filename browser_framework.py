#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器框架
从健康证管理系统中提取的独立浏览器组件
"""

import sys
import os
import time
from PyQt5.QtCore import *
from PyQt5.QtWidgets import *
from PyQt5.QtGui import *
from PyQt5.QtWebEngineWidgets import *


class WebView(QWebEngineView):
    """自定义WebView类 - 性能优化版"""
    def __init__(self, parent=None):
        super().__init__(parent)

        # 性能监控
        self.load_start_time = 0
        self.memory_usage_timer = QTimer()
        self.memory_usage_timer.timeout.connect(self.monitor_memory)
        self.memory_usage_timer.start(60000)  # 每分钟检查一次内存

        self.setup_page()

    def setup_page(self):
        """设置页面属性 - 性能优化版"""
        page = self.page()

        # 设置用户代理
        page.profile().setHttpUserAgent(
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
            "(KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        )

        # 设置页面背景色
        page.setBackgroundColor(QColor(255, 255, 255))

        # 性能优化设置
        settings = page.settings()
        try:
            # 基础设置
            settings.setAttribute(QWebEngineSettings.ErrorPageEnabled, False)
            settings.setAttribute(QWebEngineSettings.FullScreenSupportEnabled, False)
            settings.setAttribute(QWebEngineSettings.JavascriptCanOpenWindows, False)
            settings.setAttribute(QWebEngineSettings.LocalStorageEnabled, True)
            settings.setAttribute(QWebEngineSettings.LocalContentCanAccessRemoteUrls, True)

            # 性能优化设置
            settings.setAttribute(QWebEngineSettings.AcceleratedCompositingEnabled, True)
            settings.setAttribute(QWebEngineSettings.WebGLEnabled, False)  # 禁用WebGL减少内存
            settings.setAttribute(QWebEngineSettings.PluginsEnabled, False)  # 禁用插件

            # 缓存设置
            cache_size = 50 * 1024 * 1024  # 50MB缓存
            page.profile().setHttpCacheMaximumSize(cache_size)

        except Exception as e:
            print(f"WebEngine设置失败: {e}")

        # 绑定加载事件
        self.loadStarted.connect(self.on_load_started)
        self.loadFinished.connect(self.on_load_finished)

    def on_load_started(self):
        """页面开始加载"""
        self.load_start_time = time.time()

    def on_load_finished(self, success):
        """页面加载完成"""
        if self.load_start_time > 0:
            load_time = time.time() - self.load_start_time
            print(f"页面加载耗时: {load_time:.2f}秒, 成功: {success}")

            if success:
                # 延迟执行图片优化
                QTimer.singleShot(1000, self.optimize_images)

    def optimize_images(self):
        """优化页面图片"""
        try:
            self.page().runJavaScript("""
                // 图片懒加载和优化
                var images = document.querySelectorAll('img');
                images.forEach(function(img) {
                    // 设置图片渲染优化
                    img.style.imageRendering = 'auto';
                    img.style.imageRendering = 'crisp-edges';

                    // 限制图片最大尺寸
                    if (img.naturalWidth > 800) {
                        img.style.maxWidth = '800px';
                        img.style.height = 'auto';
                    }

                    // 添加加载完成事件
                    img.onload = function() {
                        this.style.opacity = '1';
                    };
                });

                // 移除不必要的元素
                var unnecessaryElements = document.querySelectorAll('script[src*="analytics"], script[src*="tracking"]');
                unnecessaryElements.forEach(function(el) {
                    el.remove();
                });
            """)
        except Exception as e:
            print(f"图片优化失败: {e}")

    def monitor_memory(self):
        """监控内存使用"""
        try:
            import psutil
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024
            memory_limit = 512  # 512MB限制

            if memory_mb > memory_limit:
                print(f"⚠ 内存使用过高: {memory_mb:.1f}MB > {memory_limit}MB，执行清理...")
                self.cleanup_memory()

        except Exception as e:
            print(f"内存监控失败: {e}")

    def cleanup_memory(self):
        """清理内存"""
        try:
            # JavaScript垃圾回收
            self.page().runJavaScript("""
                // 强制垃圾回收
                if (window.gc) window.gc();
                if (window.CollectGarbage) window.CollectGarbage();

                // 清理缓存
                if ('caches' in window) {
                    caches.keys().then(function(names) {
                        names.forEach(function(name) {
                            caches.delete(name);
                        });
                    });
                }
            """)

            # 清理HTTP缓存
            self.page().profile().clearHttpCache()

            print("✓ WebEngine内存清理完成")

        except Exception as e:
            print(f"内存清理失败: {e}")
    
    def createWindow(self, window_type):
        """处理新窗口请求"""
        # 在新标签页中打开
        print(f"创建新窗口类型: {window_type}")
        if hasattr(self.parent(), 'add_new_tab'):
            return self.parent().add_new_tab()
        return None


class BrowserFramework(QMainWindow):
    """浏览器框架主窗口"""
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("浏览器框架")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)
        
        # 创建浏览器工具栏
        toolbar_layout = self.create_browser_toolbar()
        main_layout.addLayout(toolbar_layout)
        
        # 创建标签页控件
        self.tabs = QTabWidget()
        self.tabs.setTabsClosable(True)
        self.tabs.tabCloseRequested.connect(self.close_tab)
        self.tabs.currentChanged.connect(self.on_tab_changed)
        
        # 设置标签页样式
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #ddd;
                border-radius: 8px;
                background: white;
            }
            QTabBar::tab {
                background: #f0f0f0;
                border: 1px solid #ddd;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background: white;
                border-bottom: 1px solid white;
            }
            QTabBar::tab:hover {
                background: #e0e0e0;
            }
        """)
        
        main_layout.addWidget(self.tabs)
        
        # 添加初始标签页
        self.add_initial_tab()
        
        # 初始化自动刷新相关变量
        self.auto_refresh_timer = QTimer()
        self.auto_refresh_timer.timeout.connect(self.auto_refresh_page)
        self.auto_refresh_interval = 30000
        self.is_auto_refresh_enabled = False
        
        # URL更新控制
        self.last_url_update = 0
        self.url_update_interval = 500

    def create_browser_toolbar(self):
        """创建浏览器工具栏"""
        toolbar_layout = QHBoxLayout()
        toolbar_layout.setContentsMargins(5, 5, 5, 5)

        # 后退按钮
        self.back_btn = QPushButton("←")
        self.back_btn.setFixedSize(40, 30)
        self.back_btn.setToolTip("后退")
        self.back_btn.clicked.connect(self.go_back)
        toolbar_layout.addWidget(self.back_btn)

        # 前进按钮
        self.forward_btn = QPushButton("→")
        self.forward_btn.setFixedSize(40, 30)
        self.forward_btn.setToolTip("前进")
        self.forward_btn.clicked.connect(self.go_forward)
        toolbar_layout.addWidget(self.forward_btn)

        # 刷新按钮
        self.refresh_btn = QPushButton("⟳")
        self.refresh_btn.setFixedSize(40, 30)
        self.refresh_btn.setToolTip("刷新")
        self.refresh_btn.clicked.connect(self.refresh_page)
        toolbar_layout.addWidget(self.refresh_btn)

        # 地址栏
        self.urlbar = QLineEdit()
        self.urlbar.setPlaceholderText("输入网址...")
        self.urlbar.returnPressed.connect(self.navigate_to_url)
        toolbar_layout.addWidget(self.urlbar)

        # 新标签页按钮
        self.new_tab_btn = QPushButton("+")
        self.new_tab_btn.setFixedSize(40, 30)
        self.new_tab_btn.setToolTip("新标签页")
        self.new_tab_btn.clicked.connect(lambda: self.add_new_tab())
        toolbar_layout.addWidget(self.new_tab_btn)

        return toolbar_layout

    def add_initial_tab(self):
        """添加初始标签页"""
        self.add_new_tab(QUrl('about:blank'), '新标签页')
        QTimer.singleShot(1000, self.load_default_page)

    def add_new_tab(self, qurl=QUrl('about:blank'), label='新标签页'):
        """添加新的标签页"""
        # 创建浏览器
        browser = WebView(self)

        # 如果是空白页面，不立即加载
        if qurl.toString() != 'about:blank':
            browser.load(qurl)

        # 添加标签页
        tab_index = self.tabs.addTab(browser, label)
        self.tabs.setCurrentIndex(tab_index)

        # 绑定URL变化事件
        browser.urlChanged.connect(lambda url: self.renew_urlbar(url, browser))

        # 绑定加载完成事件
        browser.loadFinished.connect(
            lambda success: self.on_load_finished(success, tab_index, browser)
        )

        return browser

    def on_load_finished(self, success, tab_index, browser):
        """页面加载完成回调"""
        if not success:
            return

        if browser != self.tabs.currentWidget():
            return

        # 更新标题
        url = browser.url().toString()
        title = browser.page().title()
        if title and len(title) > 0:
            # 限制标题长度
            display_title = title[:15] + '...' if len(title) > 15 else title
            self.tabs.setTabText(tab_index, display_title)
        else:
            self.tabs.setTabText(tab_index, '新标签页')

    def load_default_page(self):
        """加载默认页面"""
        try:
            current_browser = self.tabs.currentWidget()
            if current_browser:
                # 加载一个简单的欢迎页面
                welcome_html = """
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>浏览器框架</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            margin: 0;
                            padding: 40px;
                            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                            color: white;
                            text-align: center;
                        }
                        .container {
                            max-width: 600px;
                            margin: 0 auto;
                            background: rgba(255, 255, 255, 0.1);
                            padding: 40px;
                            border-radius: 15px;
                            backdrop-filter: blur(10px);
                        }
                        h1 {
                            font-size: 2.5em;
                            margin-bottom: 20px;
                        }
                        p {
                            font-size: 1.2em;
                            line-height: 1.6;
                            margin-bottom: 30px;
                        }
                        .features {
                            text-align: left;
                            margin: 30px 0;
                        }
                        .feature {
                            margin: 10px 0;
                            padding: 10px;
                            background: rgba(255, 255, 255, 0.1);
                            border-radius: 5px;
                        }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <h1>🌐 浏览器框架</h1>
                        <p>欢迎使用基于PyQt5和QWebEngine的浏览器框架</p>

                        <div class="features">
                            <div class="feature">✨ 多标签页浏览</div>
                            <div class="feature">🚀 性能优化</div>
                            <div class="feature">🛡️ 内存管理</div>
                            <div class="feature">🎨 自定义界面</div>
                        </div>

                        <p>在地址栏输入网址开始浏览吧！</p>
                    </div>
                </body>
                </html>
                """
                current_browser.setHtml(welcome_html)
                self.tabs.setTabText(0, "浏览器框架")
                self.urlbar.setText("浏览器框架")
        except Exception as e:
            print(f"加载默认页面失败: {e}")

    def renew_urlbar(self, url, browser=None):
        """更新地址栏显示当前标签页的URL"""
        # 非当前窗口不更新URL
        if browser != self.tabs.currentWidget():
            return

        # 限制更新频率
        current_time = time.time() * 1000
        if current_time - self.last_url_update < self.url_update_interval:
            return

        self.last_url_update = current_time

        # 更新地址栏
        url_string = url.toString()
        if url_string and url_string != 'about:blank':
            self.urlbar.setText(url_string)
        else:
            self.urlbar.setText("")

    def go_back(self):
        """后退"""
        current_browser = self.tabs.currentWidget()
        if current_browser:
            current_browser.back()

    def go_forward(self):
        """前进"""
        current_browser = self.tabs.currentWidget()
        if current_browser:
            current_browser.forward()

    def refresh_page(self):
        """刷新页面"""
        current_browser = self.tabs.currentWidget()
        if current_browser:
            current_browser.reload()

    def navigate_to_url(self):
        """导航到URL"""
        url = self.urlbar.text()
        if url:
            if not url.startswith(('http://', 'https://')):
                url = 'http://' + url
            current_browser = self.tabs.currentWidget()
            if current_browser:
                current_browser.load(QUrl(url))

    def close_tab(self, index):
        """关闭标签页"""
        if self.tabs.count() > 1:
            self.tabs.removeTab(index)
        else:
            # 如果只有一个标签页，创建新的空白页
            self.add_new_tab()
            self.tabs.removeTab(index)

    def on_tab_changed(self, index):
        """标签页切换事件"""
        current_browser = self.tabs.currentWidget()
        if current_browser:
            url = current_browser.url()
            self.renew_urlbar(url, current_browser)

    def auto_refresh_page(self):
        """自动刷新页面"""
        if self.is_auto_refresh_enabled:
            current_browser = self.tabs.currentWidget()
            if current_browser:
                current_browser.reload()

    def toggle_auto_refresh(self):
        """切换自动刷新状态"""
        self.is_auto_refresh_enabled = not self.is_auto_refresh_enabled
        if self.is_auto_refresh_enabled:
            self.auto_refresh_timer.start(self.auto_refresh_interval)
            print("自动刷新已开启")
        else:
            self.auto_refresh_timer.stop()
            print("自动刷新已关闭")

    def cleanup_memory(self):
        """清理内存"""
        try:
            # 清理所有标签页
            for i in range(self.tabs.count()):
                browser = self.tabs.widget(i)
                if browser and hasattr(browser, 'cleanup_memory'):
                    browser.cleanup_memory()

            # 系统级内存清理
            import gc
            gc.collect()

            print("✓ 浏览器内存清理完成")

        except Exception as e:
            print(f"内存清理失败: {e}")

    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            # 停止自动刷新定时器
            if hasattr(self, 'auto_refresh_timer'):
                self.auto_refresh_timer.stop()

            # 清理所有WebView
            for i in range(self.tabs.count()):
                browser = self.tabs.widget(i)
                if browser and hasattr(browser, 'cleanup_memory'):
                    browser.cleanup_memory()

            print("✓ 浏览器框架已安全关闭")

        except Exception as e:
            print(f"关闭时清理失败: {e}")

        event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用程序信息
    app.setApplicationName("浏览器框架")
    app.setApplicationVersion("1.0")

    # 创建浏览器窗口
    browser = BrowserFramework()
    browser.show()

    sys.exit(app.exec_())


if __name__ == '__main__':
    main()
