# 浏览器框架说明文档

从健康证管理系统中提取的独立浏览器组件，包含完整的浏览器功能框架。

## 文件说明

### 1. browser_framework.py
**完整版浏览器框架**
- 基于 PyQt5 + QWebEngineView
- 包含完整的网页渲染功能
- 需要安装 PyQt5WebEngine

**主要功能：**
- 多标签页浏览
- 完整的网页渲染
- 性能优化和内存管理
- JavaScript执行
- 图片优化
- 自动内存清理

**依赖要求：**
```bash
pip install PyQt5
pip install PyQtWebEngine
```

### 2. simple_browser_framework.py
**简化版浏览器框架**
- 仅使用 PyQt5 基础组件
- 不依赖 WebEngine
- 可作为浏览器框架模板

**主要功能：**
- 多标签页管理
- 浏览器工具栏界面
- 地址栏和导航按钮
- 标签页关闭功能
- 模拟页面加载

**依赖要求：**
```bash
pip install PyQt5
```

## 核心组件

### WebView 类
自定义的 QWebEngineView，包含以下优化：

1. **性能监控**
   - 页面加载时间统计
   - 内存使用监控
   - 自动内存清理

2. **页面优化**
   - 图片渲染优化
   - JavaScript垃圾回收
   - 缓存管理

3. **安全设置**
   - 禁用不必要的功能
   - 限制弹窗
   - 优化用户代理

### BrowserFramework 类
主浏览器窗口，包含：

1. **标签页管理**
   - 多标签页支持
   - 标签页关闭
   - 标签页切换

2. **导航功能**
   - 前进/后退
   - 刷新页面
   - 地址栏导航

3. **工具栏**
   - 导航按钮
   - 地址栏
   - 新标签页按钮

## 使用方法

### 运行完整版浏览器
```bash
python browser_framework.py
```

### 运行简化版浏览器
```bash
python simple_browser_framework.py
```

## 集成指南

### 1. 集成到现有项目

```python
from browser_framework import BrowserFramework, WebView

# 创建浏览器实例
browser = BrowserFramework()
browser.show()

# 或者只使用WebView组件
webview = WebView()
webview.load(QUrl("https://www.example.com"))
```

### 2. 自定义浏览器功能

```python
class CustomBrowser(BrowserFramework):
    def __init__(self):
        super().__init__()
        # 添加自定义功能
        
    def custom_navigation(self, url):
        # 自定义导航逻辑
        current_browser = self.tabs.currentWidget()
        if current_browser:
            current_browser.load(QUrl(url))
```

### 3. 扩展WebView功能

```python
class CustomWebView(WebView):
    def __init__(self, parent=None):
        super().__init__(parent)
        # 添加自定义设置
        
    def custom_optimization(self):
        # 自定义优化逻辑
        pass
```

## 主要方法

### BrowserFramework 主要方法

| 方法名 | 功能 | 参数 |
|--------|------|------|
| `add_new_tab()` | 添加新标签页 | `qurl`, `label` |
| `close_tab()` | 关闭标签页 | `index` |
| `navigate_to_url()` | 导航到URL | 无 |
| `refresh_page()` | 刷新当前页面 | 无 |
| `go_back()` | 后退 | 无 |
| `go_forward()` | 前进 | 无 |
| `cleanup_memory()` | 清理内存 | 无 |

### WebView 主要方法

| 方法名 | 功能 | 参数 |
|--------|------|------|
| `optimize_images()` | 优化页面图片 | 无 |
| `cleanup_memory()` | 清理内存 | 无 |
| `monitor_memory()` | 监控内存使用 | 无 |

## 配置选项

### 性能配置
```python
# 内存限制 (MB)
memory_limit = 512

# 缓存大小 (MB)
cache_size = 50

# 内存监控间隔 (毫秒)
memory_check_interval = 60000
```

### 界面配置
```python
# 窗口大小
window_width = 1200
window_height = 800

# 标签页样式
tab_style = """
    QTabWidget::pane {
        border: 1px solid #ddd;
        background: white;
    }
"""
```

## 注意事项

1. **WebEngine依赖**
   - 完整版需要安装 PyQtWebEngine
   - 简化版无此要求

2. **内存管理**
   - 自动内存监控和清理
   - 建议定期调用 `cleanup_memory()`

3. **性能优化**
   - 图片自动优化
   - JavaScript垃圾回收
   - 缓存管理

4. **安全考虑**
   - 已禁用不必要的功能
   - 限制弹窗和插件

## 扩展建议

1. **添加书签功能**
2. **历史记录管理**
3. **下载管理器**
4. **开发者工具**
5. **主题切换**
6. **插件系统**

## 故障排除

### 常见问题

1. **WebEngine导入失败**
   ```bash
   pip install PyQtWebEngine
   ```

2. **页面加载缓慢**
   - 检查网络连接
   - 调整缓存设置
   - 优化内存限制

3. **内存使用过高**
   - 启用自动内存清理
   - 减少同时打开的标签页
   - 调用 `cleanup_memory()`

## 版本信息

- **版本**: 1.0
- **基于**: PyQt5 + QWebEngine
- **提取自**: 健康证管理系统
- **兼容性**: Windows/macOS/Linux
