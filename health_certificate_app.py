#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
健康证管理系统
使用PyQt5实现的健康证信息录入和管理界面
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QLabel, QLineEdit, QPushButton, 
                             QComboBox, QDateEdit, QFileDialog, QMessageBox,
                             QFrame, QSizePolicy, QSpacerItem)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont, QPixmap, QIcon
from datetime import datetime, timedelta


class HealthCertificateApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.selected_image_path = ""
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("健康证管理系统")
        self.setFixedSize(760, 1032)
        self.setStyleSheet(self.get_stylesheet())
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # 添加各个区域
        self.create_basic_info_section(main_layout)
        self.create_medical_info_section(main_layout)
        self.create_photo_section(main_layout)
        self.create_action_buttons(main_layout)
        
    def create_basic_info_section(self, parent_layout):
        """创建健康证基本信息区域"""
        # 标题
        title_label = QLabel("📋 健康证基本信息")
        title_label.setObjectName("sectionTitle")
        parent_layout.addWidget(title_label)
        
        # 个人信息框架
        info_frame = QFrame()
        info_frame.setObjectName("infoFrame")
        info_layout = QVBoxLayout(info_frame)
        
        # 个人信息标题
        personal_title = QLabel("👤 个人信息")
        personal_title.setObjectName("subTitle")
        info_layout.addWidget(personal_title)
        
        # 姓名和性别行
        name_gender_layout = QHBoxLayout()

        # 姓名字段
        name_layout = QVBoxLayout()
        name_label = QLabel("姓名 *")
        name_label.setObjectName("fieldLabel")
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("请输入真实姓名")
        self.name_input.setObjectName("inputField")
        name_layout.addWidget(name_label)
        name_layout.addWidget(self.name_input)

        # 性别字段
        gender_layout = QVBoxLayout()
        gender_label = QLabel("性别 *")
        gender_label.setObjectName("fieldLabel")
        self.gender_combo = QComboBox()
        self.gender_combo.addItems(["男", "女"])
        self.gender_combo.setObjectName("comboField")
        gender_layout.addWidget(gender_label)
        gender_layout.addWidget(self.gender_combo)

        name_gender_layout.addLayout(name_layout, 2)
        name_gender_layout.addLayout(gender_layout, 1)
        info_layout.addLayout(name_gender_layout)
        
        # 身份证号字段
        id_label = QLabel("身份证号 *")
        id_label.setObjectName("fieldLabel")
        info_layout.addWidget(id_label)
        
        self.id_input = QLineEdit()
        self.id_input.setPlaceholderText("请输入18位身份证号")
        self.id_input.setObjectName("inputField")
        info_layout.addWidget(self.id_input)
        
        parent_layout.addWidget(info_frame)
        
    def create_medical_info_section(self, parent_layout):
        """创建体检信息区域"""
        # 标题
        title_label = QLabel("🏥 体检信息")
        title_label.setObjectName("sectionTitle")
        parent_layout.addWidget(title_label)
        
        # 体检信息框架
        medical_frame = QFrame()
        medical_frame.setObjectName("infoFrame")
        medical_layout = QVBoxLayout(medical_frame)
        
        # 体检单位字段
        unit_label = QLabel("体检单位 *")
        unit_label.setObjectName("fieldLabel")
        medical_layout.addWidget(unit_label)
        
        self.unit_input = QLineEdit()
        self.unit_input.setPlaceholderText("请输入体检单位全称")
        self.unit_input.setObjectName("inputField")
        medical_layout.addWidget(self.unit_input)
        
        # 体检日期和有效期行
        date_layout = QHBoxLayout()

        # 体检日期字段
        exam_date_layout = QVBoxLayout()
        exam_date_label = QLabel("体检日期 *")
        exam_date_label.setObjectName("fieldLabel")
        self.exam_date = QDateEdit()
        self.exam_date.setDate(QDate.currentDate())
        self.exam_date.setCalendarPopup(True)
        self.exam_date.setObjectName("dateField")
        self.exam_date.dateChanged.connect(self.update_expiry_date)
        exam_date_layout.addWidget(exam_date_label)
        exam_date_layout.addWidget(self.exam_date)

        # 有效期至字段
        expiry_date_layout = QVBoxLayout()
        expiry_date_label = QLabel("有效期至 *")
        expiry_date_label.setObjectName("fieldLabel")
        self.expiry_date = QDateEdit()
        self.expiry_date.setDate(QDate.currentDate().addYears(1))
        self.expiry_date.setCalendarPopup(True)
        self.expiry_date.setObjectName("dateField")
        expiry_date_layout.addWidget(expiry_date_label)
        expiry_date_layout.addWidget(self.expiry_date)

        date_layout.addLayout(exam_date_layout)
        date_layout.addLayout(expiry_date_layout)
        medical_layout.addLayout(date_layout)
        
        parent_layout.addWidget(medical_frame)
        
    def create_photo_section(self, parent_layout):
        """创建照片上传区域"""
        # 标题
        title_label = QLabel("📷 照片上传")
        title_label.setObjectName("sectionTitle")
        parent_layout.addWidget(title_label)
        
        # 照片框架
        photo_frame = QFrame()
        photo_frame.setObjectName("infoFrame")
        photo_layout = QVBoxLayout(photo_frame)
        
        # 照片选择按钮
        self.photo_button = QPushButton("📁 点击选择照片上传")
        self.photo_button.setObjectName("photoButton")
        self.photo_button.clicked.connect(self.select_photo)
        photo_layout.addWidget(self.photo_button)
        
        # 照片预览标签
        self.photo_preview = QLabel("未选择照片")
        self.photo_preview.setObjectName("photoPreview")
        self.photo_preview.setAlignment(Qt.AlignCenter)
        self.photo_preview.setMinimumHeight(120)
        photo_layout.addWidget(self.photo_preview)
        
        parent_layout.addWidget(photo_frame)
        
    def create_action_buttons(self, parent_layout):
        """创建操作按钮区域"""
        # 添加弹性空间
        spacer = QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding)
        parent_layout.addItem(spacer)
        
        # 生成健康证按钮
        self.generate_button = QPushButton("🏥 生成健康证")
        self.generate_button.setObjectName("generateButton")
        self.generate_button.clicked.connect(self.generate_certificate)
        parent_layout.addWidget(self.generate_button)
        
        # 保存健康证按钮
        self.save_button = QPushButton("💾 保存健康证")
        self.save_button.setObjectName("saveButton")
        self.save_button.clicked.connect(self.save_certificate)
        parent_layout.addWidget(self.save_button)
        
    def update_expiry_date(self):
        """更新有效期日期（体检日期+1年）"""
        exam_date = self.exam_date.date()
        expiry_date = exam_date.addYears(1)
        self.expiry_date.setDate(expiry_date)
        
    def select_photo(self):
        """选择照片文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择照片", "", 
            "图片文件 (*.png *.jpg *.jpeg *.bmp *.gif)"
        )
        
        if file_path:
            self.selected_image_path = file_path
            # 更新按钮文本
            file_name = os.path.basename(file_path)
            self.photo_button.setText(f"📁 已选择: {file_name}")
            
            # 显示照片预览
            pixmap = QPixmap(file_path)
            if not pixmap.isNull():
                scaled_pixmap = pixmap.scaled(80, 80, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                self.photo_preview.setPixmap(scaled_pixmap)
                self.photo_preview.setText("")
            else:
                self.photo_preview.setText("照片格式不支持")
                
    def validate_form(self):
        """验证表单数据"""
        if not self.name_input.text().strip():
            QMessageBox.warning(self, "验证错误", "请输入姓名")
            return False
            
        if not self.id_input.text().strip():
            QMessageBox.warning(self, "验证错误", "请输入身份证号")
            return False
            
        id_number = self.id_input.text().strip()
        if len(id_number) != 18:
            QMessageBox.warning(self, "验证错误", "身份证号必须为18位")
            return False
            
        if not self.unit_input.text().strip():
            QMessageBox.warning(self, "验证错误", "请输入体检单位")
            return False
            
        return True
        
    def generate_certificate(self):
        """生成健康证"""
        if not self.validate_form():
            return
            
        QMessageBox.information(self, "生成成功", "健康证已生成！")
        
    def save_certificate(self):
        """保存健康证"""
        if not self.validate_form():
            return

        QMessageBox.information(self, "保存成功", "健康证信息已保存！")

    def get_stylesheet(self):
        """获取样式表"""
        return """
        QMainWindow {
            background-color: #f5f5f5;
        }

        QLabel#sectionTitle {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            padding: 8px 0;
            margin-bottom: 3px;
        }

        QLabel#subTitle {
            font-size: 14px;
            font-weight: bold;
            color: #555;
            padding: 5px 0;
            margin-bottom: 3px;
        }

        QLabel#fieldLabel {
            font-size: 13px;
            color: #666;
            margin-bottom: 5px;
            margin-top: 5px;
            padding: 2px 0;
            font-weight: 500;
        }

        QFrame#infoFrame {
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 3px 0;
        }

        QLineEdit#inputField {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 13px;
            min-height: 15px;
        }

        QLineEdit#inputField:focus {
            border-color: #4CAF50;
            border-width: 2px;
        }

        QComboBox#comboField, QDateEdit#dateField {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 13px;
            min-height: 15px;
        }

        QPushButton#photoButton {
            background-color: #f8f9fa;
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 15px;
            font-size: 13px;
            color: #666;
            min-height: 20px;
        }

        QPushButton#photoButton:hover {
            background-color: #e9ecef;
            border-color: #4CAF50;
        }

        QLabel#photoPreview {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 6px;
            color: #999;
            font-size: 13px;
            padding: 10px;
            margin-top: 8px;
        }

        QPushButton#generateButton {
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 15px;
            font-size: 15px;
            font-weight: bold;
            min-height: 20px;
            margin: 3px 0;
        }

        QPushButton#generateButton:hover {
            background-color: #45a049;
        }

        QPushButton#saveButton {
            background-color: #9C27B0;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 15px;
            font-size: 15px;
            font-weight: bold;
            min-height: 20px;
            margin: 3px 0;
        }

        QPushButton#saveButton:hover {
            background-color: #8E24AA;
        }
        """


def main():
    app = QApplication(sys.argv)

    # 设置应用程序图标和信息
    app.setApplicationName("健康证管理系统")
    app.setApplicationVersion("1.0")

    window = HealthCertificateApp()
    window.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
