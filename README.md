# 健康证管理系统

基于PyQt5开发的健康证信息录入和管理界面。

## 功能特性

- 📋 **健康证基本信息录入**
  - 个人信息：姓名、性别、身份证号
  - 数据验证和格式检查

- 🏥 **体检信息管理**
  - 体检单位录入
  - 体检日期选择
  - 有效期自动计算（体检日期+1年）

- 📷 **照片上传功能**
  - 支持多种图片格式（PNG、JPG、JPEG、BMP、GIF）
  - 照片预览功能
  - 文件选择对话框

- 💾 **操作功能**
  - 生成健康证
  - 保存健康证信息

## 安装要求

### 系统要求
- Python 3.6+
- Windows/macOS/Linux

### 依赖安装

1. 安装Python（如果尚未安装）
2. 安装依赖包：

```bash
pip install -r requirements.txt
```

或者手动安装：

```bash
pip install PyQt5==5.15.9
pip install Pillow==10.0.0
```

## 运行方法

```bash
python health_certificate_app.py
```

## 界面说明

### 健康证基本信息
- **姓名**：必填项，请输入真实姓名
- **性别**：下拉选择，男/女
- **身份证号**：必填项，必须为18位有效身份证号

### 体检信息
- **体检单位**：必填项，请输入体检单位全称
- **体检日期**：可选择具体日期，默认为当前日期
- **有效期至**：自动计算，为体检日期+1年

### 照片上传
- 点击"点击选择照片上传"按钮选择照片文件
- 支持常见图片格式
- 选择后会显示照片预览

### 操作按钮
- **生成健康证**：绿色按钮，用于生成健康证
- **保存健康证**：紫色按钮，用于保存健康证信息

## 注意事项

1. 所有标记为"*"的字段都是必填项
2. 身份证号必须为18位数字
3. 照片文件大小建议不超过5MB
4. 程序会自动验证输入数据的有效性

## 技术特性

- 使用PyQt5框架开发
- 响应式界面设计
- 自定义样式表(QSS)美化
- 完整的数据验证机制
- 文件选择和预览功能

## 故障排除

### 常见问题

1. **ImportError: No module named 'PyQt5'**
   - 解决方案：运行 `pip install PyQt5`

2. **照片无法显示**
   - 检查照片格式是否支持
   - 确保照片文件没有损坏

3. **程序无法启动**
   - 检查Python版本是否为3.6+
   - 确保所有依赖都已正确安装

## 开发信息

- 开发语言：Python 3
- GUI框架：PyQt5
- 版本：1.0
- 编码：UTF-8
